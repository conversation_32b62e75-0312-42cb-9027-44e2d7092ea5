/**
 * PWA Splash Screen Handler
 */
(function () {
  "use strict";

  // Check if we're in standalone mode (PWA)
  const isStandalone =
    window.matchMedia("(display-mode: standalone)").matches ||
    window.navigator.standalone === true;

  // Only run splash screen logic in PWA mode
  if (!isStandalone) {
    return;
  }

  // Create splash screen fallback for Android and iOS
  function createSplashScreen() {
    // Check if splash screen already exists
    if (document.getElementById("pwa-splash-fallback")) {
      return;
    }

    const splashScreen = document.createElement("div");
    splashScreen.id = "pwa-splash-fallback";
    splashScreen.className = "pwa-splash-fallback";

    const splashContent = document.createElement("div");
    splashContent.className = "pwa-splash-content";

    // Get PWA settings from localized script
    const settings = window.qPWASettings || {};

    // Add splash image if available, otherwise app icon
    const splashUrl = settings.splashUrl || settings.iconUrl;
    if (splashUrl) {
      const icon = document.createElement("img");
      icon.src = splashUrl;
      icon.alt = "Splash Screen";
      icon.className = "pwa-splash-fullscreen";
      splashContent.appendChild(icon);
    }

    splashScreen.appendChild(splashContent);

    // Set CSS custom properties for theming
    if (settings.backgroundColor) {
      document.documentElement.style.setProperty(
        "--pwa-background-color",
        settings.backgroundColor
      );
      splashScreen.style.backgroundColor = settings.backgroundColor;
    }

    if (settings.themeColor) {
      document.documentElement.style.setProperty(
        "--pwa-theme-color",
        settings.themeColor
      );
    }

    // Insert at the beginning of body
    document.body.insertBefore(splashScreen, document.body.firstChild);

    return splashScreen;
  }

  // Hide splash screen
  function hideSplashScreen() {
    const splashScreen = document.getElementById("pwa-splash-fallback");
    if (splashScreen) {
      splashScreen.classList.add("hidden");
      setTimeout(() => {
        splashScreen.remove();
      }, 1200);
    }
  }

  // Initialize splash screen only on app launch
  function initSplashScreen() {
    // Check if this is the first load (app launch)
    const isFirstLoad = !sessionStorage.getItem("pwa-launched");

    if (!isFirstLoad) {
      return;
    }

    // Mark as launched for this session
    sessionStorage.setItem("pwa-launched", "true");

    // Detect iOS devices
    const isIOS =
      /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    const isStandalone = window.navigator.standalone === true;

    // Create splash screen only for iOS devices
    // Android devices handle splash screens via the manifest
    if (isIOS && isStandalone) {
      // Check if immediate splash is active
      if (window.qPWAImmediateSplashActive) {
        console.log(
          "PWA Splash: Immediate splash already active, coordinating transition"
        );

        // Get splash duration from settings - reduce it since immediate splash already showed
        const settings = window.qPWASettings || {};
        const baseSplashDuration = settings.splashDuration || 2000;
        const splashDuration = Math.max(1000, baseSplashDuration - 800); // Reduce by 800ms but minimum 1s

        // Create our splash screen with a slight delay to allow smooth transition
        setTimeout(() => {
          const splashScreen = createSplashScreen();

          // Hide splash screen when page is fully loaded
          if (document.readyState === "complete") {
            setTimeout(hideSplashScreen, splashDuration);
          } else {
            window.addEventListener("load", () => {
              setTimeout(hideSplashScreen, splashDuration);
            });
          }

          // Fallback: hide after splash duration + 500ms (reduced since we already had immediate splash)
          setTimeout(hideSplashScreen, splashDuration + 500);
        }, 200); // Slightly longer delay for smoother transition
      } else {
        // No immediate splash active, create our splash screen immediately
        const splashScreen = createSplashScreen();

        // Get splash duration from settings
        const settings = window.qPWASettings || {};
        const splashDuration = settings.splashDuration || 2000;

        // Hide splash screen when page is fully loaded
        if (document.readyState === "complete") {
          setTimeout(hideSplashScreen, splashDuration);
        } else {
          window.addEventListener("load", () => {
            setTimeout(hideSplashScreen, splashDuration);
          });
        }

        // Fallback: hide after splash duration + 1 second regardless
        setTimeout(hideSplashScreen, splashDuration + 1000);
      }
    }
  }

  // Initialize immediately to prevent blank screen
  initSplashScreen();

  // Also listen for DOMContentLoaded as a fallback
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initSplashScreen);
  }
})();
