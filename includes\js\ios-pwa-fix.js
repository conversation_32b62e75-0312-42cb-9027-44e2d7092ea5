/**
 * iOS PWA Splash Screen Fix
 * This script specifically addresses the blank screen issue on iOS PWAs
 */
(function () {
  "use strict";

  // Only run in iOS standalone mode (PWA)
  const isIOS =
    /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
  const isStandalone = window.navigator.standalone === true;

  if (!isIOS || !isStandalone) {
    return;
  }

  console.log("iOS PWA Fix: Initializing blank screen fix...");

  // Get settings from meta tags or fallback values
  function getPWASettings() {
    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
    const appleTitleMeta = document.querySelector(
      'meta[name="apple-mobile-web-app-title"]'
    );
    const appleTouchIcon = document.querySelector(
      'link[rel="apple-touch-icon"]'
    );

    return {
      backgroundColor: themeColorMeta ? themeColorMeta.content : "#ffffff",
      themeColor: themeColorMeta ? themeColorMeta.content : "#ffffff",
      appName: appleTitleMeta ? appleTitleMeta.content : document.title,
      iconUrl: appleTouchIcon ? appleTouchIcon.href : null,
    };
  }

  // Fix for iOS blank screen issue - runs immediately
  function fixIOSBlankScreen() {
    console.log("iOS PWA Fix: Applying blank screen fix...");

    // Force body and html to be visible immediately
    document.documentElement.style.display = "block";
    document.documentElement.style.visibility = "visible";
    document.documentElement.style.opacity = "1";

    // Ensure body is ready
    if (document.body) {
      document.body.style.display = "block";
      document.body.style.visibility = "visible";
      document.body.style.opacity = "1";
    } else {
      // If body doesn't exist yet, wait for it
      document.addEventListener("DOMContentLoaded", function () {
        document.body.style.display = "block";
        document.body.style.visibility = "visible";
        document.body.style.opacity = "1";
      });
    }

    // Get PWA settings from meta tags
    const settings = getPWASettings();

    // Create immediate splash screen to prevent blank screen
    const splashScreen = document.createElement("div");
    splashScreen.id = "ios-pwa-immediate-splash";
    splashScreen.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: ${settings.backgroundColor};
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: opacity 0.5s ease-out;
            -webkit-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
            overscroll-behavior: none;
        `;

    // Add app icon if available
    if (settings.iconUrl) {
      const icon = document.createElement("img");
      icon.src = settings.iconUrl;
      icon.alt = settings.appName || "Loading";
      icon.style.cssText = `
                max-width: 120px;
                max-height: 120px;
                width: 25vw;
                height: 25vw;
                object-fit: contain;
                animation: iosPulse 2s infinite ease-in-out;
                border-radius: 22.5%;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                background-color: ${settings.themeColor};
            `;

      // Handle icon loading
      icon.onload = function () {
        console.log("iOS PWA Fix: Icon loaded successfully");
      };

      icon.onerror = function () {
        console.warn("iOS PWA Fix: Failed to load icon");
        // Replace with a simple colored circle
        const fallbackIcon = document.createElement("div");
        fallbackIcon.style.cssText = `
                    width: 120px;
                    height: 120px;
                    background-color: ${settings.themeColor};
                    border-radius: 22.5%;
                    animation: iosPulse 2s infinite ease-in-out;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;
        icon.parentNode.replaceChild(fallbackIcon, icon);
      };

      splashScreen.appendChild(icon);
    } else {
      // Create a simple fallback icon
      const fallbackIcon = document.createElement("div");
      fallbackIcon.style.cssText = `
                width: 120px;
                height: 120px;
                background-color: ${settings.themeColor};
                border-radius: 22.5%;
                animation: iosPulse 2s infinite ease-in-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
      splashScreen.appendChild(fallbackIcon);
    }

    // Add pulse animation styles
    const style = document.createElement("style");
    style.textContent = `
            @keyframes iosPulse {
                0% { transform: scale(0.95); opacity: 0.8; }
                50% { transform: scale(1.05); opacity: 1; }
                100% { transform: scale(0.95); opacity: 0.8; }
            }
        `;
    document.head.appendChild(style);

    // Add splash screen to DOM immediately
    if (document.body) {
      document.body.appendChild(splashScreen);
    } else {
      // If body doesn't exist, add it when DOM is ready
      document.addEventListener("DOMContentLoaded", function () {
        document.body.appendChild(splashScreen);
      });
    }

    console.log("iOS PWA Fix: Immediate splash screen created");

    // Remove splash screen after page loads and regular splash screen takes over
    function removeSplashScreen() {
      console.log("iOS PWA Fix: Removing immediate splash screen");
      if (splashScreen && splashScreen.parentNode) {
        // Add a smooth fade out transition
        splashScreen.style.transition = "opacity 0.5s ease-out";
        splashScreen.style.opacity = "0";
        setTimeout(function () {
          if (splashScreen.parentNode) {
            splashScreen.parentNode.removeChild(splashScreen);
          }
        }, 500); // Increased timeout to match transition duration
      }
    }

    // Set a flag to indicate immediate splash is active
    window.qPWAImmediateSplashActive = true;

    // Remove splash screen when page is loaded or after timeout
    if (document.readyState === "complete") {
      setTimeout(removeSplashScreen, 800); // Increased delay to allow proper transition
    } else {
      window.addEventListener("load", function () {
        setTimeout(removeSplashScreen, 800);
      });

      // Fallback timeout - reduced since we want faster transition
      setTimeout(removeSplashScreen, 2000);
    }

    // Also remove when regular splash screen is created
    const observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        mutation.addedNodes.forEach(function (node) {
          if (
            node.nodeType === 1 &&
            (node.id === "pwa-splash-fallback" ||
              node.classList?.contains("pwa-splash-fallback") ||
              node.id === "q-pwa-splash-screen" ||
              node.classList?.contains("q-pwa-splash"))
          ) {
            console.log(
              "iOS PWA Fix: Regular splash screen detected, removing immediate splash"
            );
            removeSplashScreen();
            observer.disconnect();
          }
        });
      });
    });

    if (document.body) {
      observer.observe(document.body, { childList: true, subtree: true });
    } else {
      document.addEventListener("DOMContentLoaded", function () {
        observer.observe(document.body, { childList: true, subtree: true });
      });
    }

    // Update the removeSplashScreen function to clear the flag
    const originalRemoveSplashScreen = removeSplashScreen;
    removeSplashScreen = function () {
      window.qPWAImmediateSplashActive = false;
      originalRemoveSplashScreen();
    };
  }

  // Run immediately - don't wait for DOM
  fixIOSBlankScreen();

  // Fix for iOS navigation issues in PWA mode
  window.addEventListener("load", function () {
    // Ensure links work properly in standalone mode
    document.addEventListener("click", function (event) {
      let target = event.target;

      // Find closest anchor tag
      while (target && target.tagName !== "A") {
        target = target.parentElement;
      }

      if (target && target.tagName === "A") {
        const href = target.getAttribute("href");

        // Only handle internal links
        if (
          href &&
          href.indexOf("http") !== 0 &&
          href.indexOf("#") !== 0 &&
          href.indexOf("mailto:") !== 0 &&
          href.indexOf("tel:") !== 0
        ) {
          event.preventDefault();
          window.location.href = href;
        }
      }
    });
  });
})();
